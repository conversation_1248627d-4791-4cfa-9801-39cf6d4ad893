<?php
// Set performance headers first, before any output
require_once 'includes/performance.php';
Performance::setPerformanceHeaders();

// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/additional_models.php';

$imageModel = new Image();

// Get only display images for the main gallery (max 20)
$galleryImages = $imageModel->getGalleryDisplayImages();

// Get category filter for navigation
$category = isset($_GET['category']) ? Utils::sanitizeInput($_GET['category']) : 'all';

// Filter images if category is specified
if ($category !== 'all' && !empty($galleryImages)) {
    $galleryImages = array_filter($galleryImages, function($image) use ($category) {
        return isset($image['image_category']) && $image['image_category'] === $category;
    });
}

// Set SEO data for gallery page
$seoTitle = 'Safari Photo Gallery - African Wildlife & Landscapes';
$seoDescription = 'Explore our stunning safari photo gallery showcasing Kenya\'s incredible wildlife, breathtaking landscapes, and authentic African experiences captured during our tours.';
$seoKeywords = 'Kenya safari photos, African wildlife gallery, safari photography, Maasai Mara photos, Kenya landscapes, wildlife images, safari gallery';
$seoImage = 'images/gallery-hero.jpg';
$seoType = 'website';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <link rel="stylesheet" href="style.css" />
    <style>
        /* ===================================
           GALLERY PAGE SPECIFIC STYLES
           =================================== */

        .gallery-grid {
            columns: 3;
            column-gap: 1.5rem;
            column-fill: balance;
        }

        @media (max-width: 1024px) {
            .gallery-grid {
                columns: 2;
                column-gap: 1.25rem;
            }
        }

        @media (max-width: 640px) {
            .gallery-grid {
                columns: 1;
                column-gap: 1rem;
            }
        }

        .gallery-item {
            position: relative;
            overflow: hidden;
            border-radius: 1rem;
            transition: all 0.3s ease;
            cursor: pointer;
            break-inside: avoid;
            margin-bottom: 1.5rem;
            display: inline-block;
            width: 100%;
            background: #f8f9fa;
        }

        @media (max-width: 1024px) {
            .gallery-item {
                margin-bottom: 1.25rem;
            }
        }

        @media (max-width: 640px) {
            .gallery-item {
                margin-bottom: 1rem;
            }
        }

        .gallery-item:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }

        .gallery-image {
            width: 100%;
            height: auto;
            display: block;
            transition: transform 0.3s ease;
            border-radius: 1rem;
        }

        .gallery-item:hover .gallery-image {
            transform: scale(1.02);
        }

        .gallery-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.85));
            color: white;
            padding: 1.25rem;
            transform: translateY(100%);
            transition: all 0.3s ease;
            backdrop-filter: blur(2px);
            border-radius: 0 0 1rem 1rem;
        }

        .gallery-item:hover .gallery-overlay {
            transform: translateY(0);
        }

        .gallery-overlay h3 {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            line-height: 1.3;
        }

        .gallery-overlay p {
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
            font-size: 0.8rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .gallery-overlay .gallery-meta {
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            font-size: 0.7rem;
            opacity: 0.75;
        }

        .lightbox {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(5px);
        }

        .lightbox.active {
            display: flex;
            animation: fadeIn 0.3s ease-out;
        }

        .lightbox-content {
            position: relative;
            animation: scaleIn 0.3s ease-out;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-close {
            position: absolute;
            top: -50px;
            right: -10px;
            color: white;
            font-size: 2rem;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            border: none;
            padding: 0.75rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            z-index: 1001;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .lightbox-close:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        /* Mobile adjustments for close button */
        @media (max-width: 768px) {
            .lightbox-close {
                top: 10px;
                right: 10px;
                font-size: 1.5rem;
                width: 40px;
                height: 40px;
                padding: 0.5rem;
            }
        }

        .lightbox img {
            width: auto;
            height: auto;
            max-width: min(90vw, 1200px);
            max-height: min(90vh, 800px);
            border-radius: 0.5rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            object-fit: contain;
            display: block;
            transition: all 0.3s ease;
        }

        /* Ensure consistent sizing for different aspect ratios */
        .lightbox img.portrait {
            max-width: min(70vw, 600px);
            max-height: min(90vh, 800px);
        }

        .lightbox img.landscape {
            max-width: min(90vw, 1200px);
            max-height: min(70vh, 600px);
        }

        .lightbox img.square {
            max-width: min(80vw, 800px);
            max-height: min(80vh, 800px);
        }

        /* Mobile responsive adjustments */
        @media (max-width: 768px) {
            .lightbox img.portrait {
                max-width: min(85vw, 400px);
                max-height: min(85vh, 600px);
            }

            .lightbox img.landscape {
                max-width: min(95vw, 600px);
                max-height: min(60vh, 400px);
            }

            .lightbox img.square {
                max-width: min(90vw, 500px);
                max-height: min(75vh, 500px);
            }
        }

        /* Loading state for gallery images */
        .gallery-image:not(.image-loaded) {
            background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            min-height: 200px;
        }

        /* Loading state for lightbox images */
        .lightbox img:not([src]),
        .lightbox img.loading {
            background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            min-width: 200px;
            min-height: 150px;
        }

        /* Masonry layout improvements */
        .gallery-grid .gallery-item:first-child {
            margin-top: 0;
        }

        /* Ensure smooth loading */
        .gallery-image {
            opacity: 0;
            transition: opacity 0.4s ease, transform 0.3s ease;
        }

        .gallery-image.image-loaded {
            opacity: 1;
        }

        /* Shimmer animation for loading state */
        @keyframes shimmer {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes scaleIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }

        /* Hero Section Styles */
        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(249, 115, 22, 0.3));
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .parallax-bg {
            background-attachment: fixed;
        }

        @media (max-width: 768px) {
            .parallax-bg {
                background-attachment: scroll;
            }
        }

        /* Additional masonry improvements */
        .gallery-grid {
            width: 100%;
        }

        /* Prevent layout shift during image loading */
        .gallery-item {
            min-height: 150px;
        }

        /* Smooth transitions for hover effects */
        .gallery-item {
            will-change: transform;
        }

        /* Better spacing for mobile */
        @media (max-width: 640px) {
            .gallery-item {
                margin-bottom: 0.75rem;
                min-height: 120px;
            }
        }

        /* Ensure images don't exceed container width */
        .gallery-image {
            max-width: 100%;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="relative bg-cover bg-center bg-no-repeat parallax-bg py-20 px-4" style="background-image: url('images/nav-bg.jpg')">
        <div class="absolute inset-0 hero-overlay"></div>

        <!-- Hero Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center text-white">
            <h1 class="text-3xl md:text-5xl font-bold mb-6 text-shadow">
                Our <span class="text-orange-400">Gallery</span>
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8"></div>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto text-shadow">
                A visual journey through Africa's most breathtaking moments
            </p>
        </div>
    </section>

    <!-- Gallery Section -->
    <section class="py-16 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <!-- Gallery Grid -->
            <div class="gallery-grid" id="gallery-container">
                <?php if (empty($galleryImages)): ?>
                    <!-- Empty State -->
                    <div class="col-span-full text-center py-16">
                        <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-images text-gray-400 text-3xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">No Images Available</h3>
                        <p class="text-lg md:text-xl text-gray-600 mb-4 max-w-md mx-auto">
                            Our gallery is currently being updated. Images need to be uploaded and set for display in the admin panel.
                        </p>
                        <p class="text-sm text-gray-500 mb-8 max-w-md mx-auto">
                            Admin: Upload images and mark them for gallery display to populate this section.
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <a href="index.php" class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-lg hover:opacity-90 transition-opacity">
                                <i class="fas fa-home mr-2"></i>
                                Back to Home
                            </a>
                            <a href="admin-dashboard/images.php" class="inline-flex items-center px-6 py-3 border-2 border-orange-500 text-orange-500 rounded-lg hover:bg-orange-500 hover:text-white transition-colors">
                                <i class="fas fa-cog mr-2"></i>
                                Admin Panel
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($galleryImages as $index => $image): ?>
                        <div class="gallery-item image-hover-zoom" onclick="openLightbox(<?php echo $index; ?>)">
                            <img data-src="<?php echo htmlspecialchars($image['url']); ?>"
                                 alt="<?php echo Utils::displayText($image['alt_text']); ?>"
                                 class="gallery-image lazy-image"
                                 loading="lazy"
                                 decoding="async">
                            <div class="gallery-overlay">
                                <?php
                                // Extract title and description from alt_text or create meaningful content
                                $altText = $image['alt_text'] ?? 'Gallery Image';
                                $title = $altText;
                                $description = 'Experience the beauty of Africa through our lens';

                                // If alt_text contains a dash, split it into title and description
                                if (strpos($altText, ' - ') !== false) {
                                    $parts = explode(' - ', $altText, 2);
                                    $title = trim($parts[0]);
                                    $description = trim($parts[1]);
                                } elseif (strpos($altText, ':') !== false) {
                                    $parts = explode(':', $altText, 2);
                                    $title = trim($parts[0]);
                                    $description = trim($parts[1]);
                                } else {
                                    // Create a description based on common safari terms
                                    $safariTerms = [
                                        'safari' => 'Witness the incredible wildlife of Africa',
                                        'wildlife' => 'Amazing animals in their natural habitat',
                                        'landscape' => 'Breathtaking views of African wilderness',
                                        'sunset' => 'Golden hour magic in the African savanna',
                                        'elephant' => 'Majestic giants of the African plains',
                                        'lion' => 'The king of the jungle in action',
                                        'giraffe' => 'Graceful giants reaching for the sky',
                                        'zebra' => 'Striped beauties of the savanna',
                                        'maasai' => 'Cultural heritage and natural beauty combined',
                                        'kilimanjaro' => 'Africa\'s highest peak and greatest adventure'
                                    ];

                                    foreach ($safariTerms as $term => $desc) {
                                        if (stripos($altText, $term) !== false) {
                                            $description = $desc;
                                            break;
                                        }
                                    }
                                }
                                ?>
                                <h3 class="text-lg font-bold mb-2"><?php echo Utils::displayText($title); ?></h3>
                                <p class="text-sm opacity-90"><?php echo Utils::displayText($description); ?></p>
                                <div class="mt-3 flex items-center text-xs opacity-75">
                                    <i class="fas fa-camera mr-1"></i>
                                    <span>Click to view full size</span>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="relative py-20 px-4 text-white overflow-hidden">
        <!-- Background Image -->
        <div class="absolute inset-0 bg-cover bg-center bg-no-repeat" style="background-image: url('images/cta-banner.jpg'); filter: blur(2px);">
            <!-- Dark Overlay for better text readability -->
            <div class="absolute inset-0 bg-black bg-opacity-80"></div>
        </div>

        <!-- Content -->
        <div class="relative z-10 max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-semibold mb-6 text-white drop-shadow-lg">
                Inspired by What You See?
            </h2>
            <p class="text-lg md:text-xl mb-10 text-white drop-shadow-md max-w-3xl mx-auto leading-relaxed">
                These moments could be yours. Let us create your personalized African adventure and capture memories that will last a lifetime.
            </p>
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <a href="request-quote.php" class="bg-gradient-to-r from-orange-500 to-red-500 text-white hover:from-orange-600 hover:to-red-600 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl">
                    Plan Your Adventure
                </a>
                <a href="tours.php" class="border-2 border-white text-white hover:bg-white hover:text-orange-500 px-10 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105 shadow-xl">
                    View Our Tours
                </a>
            </div>
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 border-2 border-white opacity-20 rounded-full"></div>
        <div class="absolute bottom-10 right-10 w-16 h-16 border-2 border-orange-400 opacity-30 rounded-full"></div>
        <div class="absolute top-1/2 left-5 w-3 h-3 bg-orange-400 opacity-40 rounded-full"></div>
        <div class="absolute top-1/4 right-20 w-2 h-2 bg-white opacity-50 rounded-full"></div>
    </section>

    <!-- Lightbox -->
    <div class="lightbox" id="lightbox">
        <div class="lightbox-content">
            <button class="lightbox-close" onclick="closeLightbox()">&times;</button>
            <img id="lightbox-image" src="" alt="" class="max-w-full max-h-full">
        </div>
    </div>



    <!-- Scripts -->
    <script>
        // ===================================
        // GALLERY PAGE SPECIFIC JAVASCRIPT
        // ===================================

        // Gallery images data from PHP
        const galleryImages = <?php echo json_encode($galleryImages); ?>;

        // Lightbox functions
        function openLightbox(index) {
            const lightbox = document.getElementById('lightbox');
            const lightboxImage = document.getElementById('lightbox-image');

            if (galleryImages[index]) {
                lightboxImage.src = galleryImages[index].url;
                lightboxImage.alt = galleryImages[index].alt_text;

                // Remove any existing aspect ratio classes
                lightboxImage.classList.remove('portrait', 'landscape', 'square');

                // Add load event listener to determine aspect ratio and apply appropriate class
                lightboxImage.onload = function() {
                    const aspectRatio = this.naturalWidth / this.naturalHeight;

                    if (aspectRatio < 0.8) {
                        // Portrait orientation
                        this.classList.add('portrait');
                    } else if (aspectRatio > 1.3) {
                        // Landscape orientation
                        this.classList.add('landscape');
                    } else {
                        // Square or near-square
                        this.classList.add('square');
                    }

                    // Remove loading state
                    this.classList.remove('loading');
                };

                // Add error handling
                lightboxImage.onerror = function() {
                    console.error('Failed to load lightbox image:', this.src);
                    this.alt = 'Image failed to load';
                    this.classList.remove('loading');
                };

                // Add loading state
                lightboxImage.classList.add('loading');

                lightbox.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeLightbox() {
            const lightbox = document.getElementById('lightbox');
            lightbox.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        // Lazy loading implementation for masonry layout
        function initializeLazyLoading() {
            const lazyImages = document.querySelectorAll('.lazy-image');

            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        const src = img.getAttribute('data-src');

                        if (src) {
                            img.src = src;
                            img.onload = function() {
                                img.classList.add('image-loaded');
                                img.removeAttribute('data-src');
                            };
                            img.onerror = function() {
                                img.classList.add('image-error');
                                img.alt = 'Image failed to load';
                            };
                        }

                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            lazyImages.forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Event listeners
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize lazy loading
            initializeLazyLoading();

            // Close lightbox with Escape key
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeLightbox();
                }
            });

            // Close lightbox when clicking outside image
            const lightbox = document.getElementById('lightbox');
            if (lightbox) {
                lightbox.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeLightbox();
                    }
                });
            }

            // Add staggered animation to gallery items
            const galleryItems = document.querySelectorAll('.gallery-item');
            galleryItems.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.05}s`;
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    item.style.opacity = '1';
                    item.style.transform = 'translateY(0)';
                }, index * 50);
            });
        });
    </script>

    <!-- Image Optimization -->
    <script src="js/image-optimizer.js"></script>

    <?php include 'footer.php'; ?>
</body>
</html>