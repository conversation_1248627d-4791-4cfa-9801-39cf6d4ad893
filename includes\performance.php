<?php
/**
 * Performance Optimization Helper for Meleva Tours and Travel
 * Provides performance improvements for better Core Web Vitals and SEO
 */

class Performance {
    
    /**
     * Generate critical CSS for above-the-fold content
     */
    public static function generateCriticalCSS() {
        return '
        <style>
        /* Critical CSS for above-the-fold content */
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body, html { font-family: "Segoe UI", sans-serif; scroll-behavior: smooth; }
        
        /* Navigation critical styles */
        #main-nav { 
            position: fixed; top: 0; left: 0; right: 0; z-index: 50; 
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);
        }
        #main-nav.has-hero-section { background-color: transparent; box-shadow: none; }
        #main-nav.no-hero-section { background-color: white; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); }
        #main-nav.scrolled { background-color: rgba(255, 255, 255, 0.95); box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); }
        
        /* Hero section critical styles */
        .hero-overlay { 
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(249, 115, 22, 0.3)); 
        }
        .text-shadow { text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5); }
        .gradient-text { 
            background: linear-gradient(135deg, #f97316, #ea580c);
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Image loading optimization */
        img { opacity: 0; transition: opacity 0.3s ease; }
        img.image-loaded, img[src]:not([data-src]) { opacity: 1; }
        img.image-error { opacity: 0.5; filter: grayscale(100%); }
        
        /* Lazy loading placeholder */
        .lazy-image:not(.image-loaded) {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }
        
        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        </style>';
    }
    
    /**
     * Generate resource hints for better loading performance
     */
    public static function generateResourceHints() {
        return '
        <!-- DNS Prefetch for external resources -->
        <link rel="dns-prefetch" href="//cdn.tailwindcss.com">
        <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
        <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
        <link rel="dns-prefetch" href="//fonts.googleapis.com">
        
        <!-- Preconnect to critical external domains -->
        <link rel="preconnect" href="https://cdn.tailwindcss.com" crossorigin>
        <link rel="preconnect" href="https://cdnjs.cloudflare.com" crossorigin>
        
        <!-- Preload critical resources -->
        <link rel="preload" href="style.css" as="style">
        <link rel="preload" href="js/global.js" as="script">
        <link rel="preload" href="images/meleva-lg.png" as="image">
        ';
    }
    
    /**
     * Generate Web Vitals monitoring script
     */
    public static function generateWebVitalsScript() {
        return '
        <script>
        // Core Web Vitals monitoring
        function sendToAnalytics(metric) {
            // Send to your analytics service
            console.log("Web Vital:", metric.name, metric.value);
            
            // Example: Send to Google Analytics 4
            if (typeof gtag !== "undefined") {
                gtag("event", metric.name, {
                    event_category: "Web Vitals",
                    event_label: metric.id,
                    value: Math.round(metric.name === "CLS" ? metric.value * 1000 : metric.value),
                    non_interaction: true,
                });
            }
        }
        
        // Load web-vitals library and measure
        (function() {
            const script = document.createElement("script");
            script.src = "https://unpkg.com/web-vitals@3/dist/web-vitals.iife.js";
            script.onload = function() {
                webVitals.getCLS(sendToAnalytics);
                webVitals.getFID(sendToAnalytics);
                webVitals.getFCP(sendToAnalytics);
                webVitals.getLCP(sendToAnalytics);
                webVitals.getTTFB(sendToAnalytics);
            };
            document.head.appendChild(script);
        })();
        </script>';
    }
    
    /**
     * Optimize image loading with intersection observer
     */
    public static function generateImageOptimizationScript() {
        return '
        <script>
        // Enhanced image loading optimization
        (function() {
            // Create intersection observer for lazy loading
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        
                        // Load the image
                        if (img.dataset.src) {
                            const tempImg = new Image();
                            tempImg.onload = () => {
                                img.src = img.dataset.src;
                                if (img.dataset.srcset) {
                                    img.srcset = img.dataset.srcset;
                                }
                                img.classList.add("image-loaded");
                                delete img.dataset.src;
                                delete img.dataset.srcset;
                            };
                            tempImg.onerror = () => {
                                img.classList.add("image-error");
                            };
                            tempImg.src = img.dataset.src;
                        }
                        
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: "50px 0px",
                threshold: 0.01
            });
            
            // Observe all lazy images
            document.addEventListener("DOMContentLoaded", () => {
                document.querySelectorAll("img[data-src]").forEach(img => {
                    imageObserver.observe(img);
                });
            });
            
            // Preload critical images
            const criticalImages = document.querySelectorAll(".critical-image, .hero-image, .logo");
            criticalImages.forEach(img => {
                if (img.dataset.src) {
                    const link = document.createElement("link");
                    link.rel = "preload";
                    link.as = "image";
                    link.href = img.dataset.src;
                    document.head.appendChild(link);
                }
            });
        })();
        </script>';
    }
    
    /**
     * Generate service worker for caching
     */
    public static function generateServiceWorkerScript() {
        return '
        <script>
        // Register service worker for caching
        if ("serviceWorker" in navigator) {
            window.addEventListener("load", () => {
                navigator.serviceWorker.register("/sw.js")
                    .then(registration => {
                        console.log("SW registered: ", registration);
                    })
                    .catch(registrationError => {
                        console.log("SW registration failed: ", registrationError);
                    });
            });
        }
        </script>';
    }
    
    /**
     * Minify HTML output
     */
    public static function minifyHTML($html) {
        // Remove comments (except IE conditionals)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);
        
        // Remove whitespace between tags
        $html = preg_replace('/>\s+</', '><', $html);
        
        // Remove extra whitespace
        $html = preg_replace('/\s+/', ' ', $html);
        
        // Trim
        $html = trim($html);
        
        return $html;
    }
    
    /**
     * Generate performance monitoring headers
     */
    public static function setPerformanceHeaders() {
        // Only set headers if they haven't been sent yet
        if (!headers_sent()) {
            // Server timing header for debugging
            header('Server-Timing: total;dur=' . (microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']) * 1000);

            // Performance hints
            header('Link: </style.css>; rel=preload; as=style', false);
            header('Link: </js/global.js>; rel=preload; as=script', false);
            header('Link: </images/meleva-lg.png>; rel=preload; as=image', false);

            // Security headers for performance
            header('X-Content-Type-Options: nosniff');
            header('X-Frame-Options: SAMEORIGIN');
            header('X-XSS-Protection: 1; mode=block');

            // Cache control
            if (strpos($_SERVER['REQUEST_URI'], '.') !== false) {
                // Static assets
                header('Cache-Control: public, max-age=31536000, immutable');
            } else {
                // HTML pages
                header('Cache-Control: public, max-age=3600');
            }
        }
    }
    
    /**
     * Generate critical resource preloads based on page type
     */
    public static function generatePageSpecificPreloads($pageType = 'default') {
        $preloads = [];
        
        switch ($pageType) {
            case 'home':
                $preloads[] = '<link rel="preload" href="images/hero-bg.jpg" as="image">';
                $preloads[] = '<link rel="preload" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" as="style">';
                break;
                
            case 'tours':
                $preloads[] = '<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css" as="style">';
                break;
                
            case 'gallery':
                $preloads[] = '<link rel="preload" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js" as="script">';
                break;
                
            case 'details':
                $preloads[] = '<link rel="preload" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" as="style">';
                $preloads[] = '<link rel="preload" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js" as="script">';
                break;
        }
        
        return implode("\n", $preloads);
    }
}
